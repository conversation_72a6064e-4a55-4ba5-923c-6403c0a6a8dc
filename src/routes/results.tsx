import { getSearchResults } from "@/api";
import { STANDARD_TEST_TABLE_VIEW } from "@/components/table/table_views";
import { TestCase, TestCaseTable } from "@/components/table/testcase-table";
import { Spinner } from "@/components/ui/spinner";
import { createFileRoute } from "@tanstack/react-router";
import { z } from "zod";

export const searchSchema = z.object({
  // basic
  branch: z.string().optional(),
  regLevel: z.string().optional(),
  lastStatus: z.string().optional(),
  ownerTest: z.string().optional(),
  parentsuite: z.string().optional(),
  suite: z.string().optional(),
  action: z.string().optional(),
  testCase: z.string().optional(),
  cmd: z.string().optional(),
  branch_id: z.number().optional(),
  tasId: z.number().optional(),

  // advanced left
  owner: z.string().optional(),
  reason: z.string().optional(),
  build: z.string().optional(),
  physiology: z.string().optional(),
  platform: z.string().optional(),
  vmPlatform: z.string().optional(),
  subSystem: z.string().optional(),
  dumpPerTopo: z
    .number()
    .refine((val) => val === 0 || val === 1, {
      message: "dumpPerTopo must be 0 or 1",
    })
    .optional(),
  caseInsensitive: z.boolean().optional(),

  // advanced middle
  errorClassSeverity: z.string().optional(),
  tag: z.string().optional(),
  team: z.string().optional(),
  beforeBuild: z.string().optional(),
  subTopology: z.string().optional(),
  topoStatus: z.string().optional(),
  vmHypervisor: z.string().optional(),
  subSystemBranch: z.string().optional(),
  skipExpandAndGhost: z.boolean().optional(),
  vmTopoAggr: z.boolean().optional(),

  // advanced right
  infoLevel: z.string().optional(),
  framework: z.string().optional(),
  lastChangedSince: z.string().optional(),
  fid: z.string().optional(),
  pressInclude: z.string().optional(),
  location: z.string().optional(),
  dts: z.string().optional(),
  afterBuild: z.string().optional(),
  extraKey: z.string().optional(),
  lastXDays: z.string().optional(),
  vmInterface: z.string().optional(),
  topoReason: z.string().optional(),
  ignoreInvalidSubtopos: z.boolean().optional(),
});

export type SearchParams = z.infer<typeof searchSchema>;

export const Route = createFileRoute("/results")({
  component: RouteComponent,
  validateSearch: (search: Record<string, unknown>) => searchSchema.parse(search),
  loaderDeps: ({ search }) => search,
  loader: async ({ deps }) => {
    return await getSearchResults(deps);
  },

  pendingComponent: () => (
    <div className="w-full grow flex justify-center items-center">
      <Spinner size="large" className="text-primary" />
    </div>
  ),
});

function RouteComponent() {
  const testData = Route.useLoaderData();
  return <TestCaseTable view={STANDARD_TEST_TABLE_VIEW} data={testData as TestCase[]} title="DUMP PER TESTCASE" />;
}
