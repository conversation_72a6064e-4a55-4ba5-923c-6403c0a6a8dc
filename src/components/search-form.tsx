"use client";

import { useMemo, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { Search, X } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Combobox } from "@/components/combo-box";
import {
  useOwnerQuery,
  useParentSuiteQuery,
  useSuiteQuery,
  // 新增三個 Combobox hooks
  useTeamQuery,
  usePhysTopoQuery,
  useSubTopoQuery,
} from "@/api/queries";
import { useQueryClient, useQuery } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import { Input } from "./ui/input";
import { Checkbox } from "@/components/ui/checkbox";

type SearchFormProps = {
  branches: string[];
};

export type Option = {
  name: string;
  value: string;
};

export const statusOptions: Option[] = [
  { name: "PASSED", value: "PASSED" },
  { name: "!PASSED", value: "!PASSED" },
  { name: "FAILED", value: "FAILED" },
  { name: "!FAILED", value: "!FAILED" },
  { name: "ROTTEN", value: "ROTTEN" },
  { name: "!ROTTEN", value: "!ROTTEN" },
  { name: "GHOST", value: "GHOST" },
  { name: "!GHOST", value: "!GHOST" },
  { name: "OUT", value: "OUT" },
  { name: "!OUT", value: "!OUT" },
  { name: "SKIPPED", value: "SKIPPED" },
  { name: "!SKIPPED", value: "!SKIPPED" },
];

export const actionOptions: Option[] = [
  { name: "empty", value: "empty" },
  { name: "!empty", value: "!empty" },
];

const infoLevelOptions: Option[] = [
  { name: "testOnly", value: "testOnly" },
  { name: "full", value: "full" },
];

const regressIncludeOptions: Option[] = [
  { name: "true", value: "true" },
  { name: "!true", value: "!true" },
  { name: "false", value: "false" },
  { name: "beta", value: "beta" },
  { name: "unknown", value: "unknown" },
];

// 可搜尋 Location 的固定清單
const LOCATION_CODES = [
  "an", "at", "av", "ba", "br", "ch", "es", "ka", "mu", "mv",
  "na", "no", "ot", "pu", "su", "uk", "wf",
];
// 讓 Combobox 能吃本地清單
function useLocationQuery(term: string) {
  return useQuery({
    queryKey: ["location", term],
    queryFn: async () => {
      const t = (term ?? "").toLowerCase();
      return LOCATION_CODES.filter((c) => !t || c.includes(t));
    },
  });
}

export function SearchForm({ branches }: SearchFormProps) {
  useQueryClient();

  const [_, setActiveTab] = useState("basic");

  // Basic tab
  const [parentSuiteValue, setParentSuiteValue] = useState("");
  const [ownerValue, setOwnerValue] = useState("");
  const [suiteValue, setSuiteValue] = useState("");
  const [branch, setBranch] = useState("");
  const [status, setStatus] = useState("");
  const [action, setAction] = useState("");
  const [reglevel, setReglevel] = useState("");
  const [testcase, setTestCase] = useState("");

  // Advanced tab — left column
  const [ownerAdv, setOwnerAdv] = useState("");
  const [reason, setReason] = useState("");
  const [build, setBuild] = useState("");
  const [physiology, setPhysiology] = useState("");
  const [platform, setPlatform] = useState("");
  const [vmPlatform, setVmPlatform] = useState("None");
  const [subSystem, setSubSystem] = useState("");
  const [dumpPerTopo, setDumpPerTopo] = useState(false);
  const [caseInsensitive, setCaseInsensitive] = useState(false);

  // Advanced tab — middle column
  const [errorClassSeverity, setErrorClassSeverity] = useState("");
  const [tag, setTag] = useState("");
  const [team, setTeam] = useState("");
  const [beforeBuild, setBeforeBuild] = useState("");
  const [subTopology, setSubTopology] = useState("");
  const [topoStatus, setTopoStatus] = useState("");
  const [vmHypervisor, setVmHypervisor] = useState("None");
  const [subSystemBranch, setSubSystemBranch] = useState("");
  const [skipExpandAndGhost, setSkipExpandAndGhost] = useState(true);
  const [vmTopoAggr, setVmTopoAggr] = useState(false);

  // Advanced tab — right column
  const [infoLevel, setInfoLevel] = useState("testOnly");
  const [framework, setFramework] = useState("");
  const [lastChangedSince, setLastChangedSince] = useState(""); // yyyy-mm-dd
  const [fid, setFid] = useState("");
  const [regressInclude, setRegressInclude] = useState("");
  const [location, setLocation] = useState("");
  const [dts, setDts] = useState("");
  const [afterBuild, setAfterBuild] = useState("");
  const [extraKey, setExtraKey] = useState("");
  const [lastXDays, setLastXDays] = useState("");
  const [vmInterface, setVmInterface] = useState("None");
  const [ignoreInvalidSubtopos, setIgnoreInvalidSubtopos] = useState(false);

  // --- API-backed option lists ---
  const fetchJSON = async (url: string) =>
    (await fetch(url, { credentials: "include", headers: { "Content-type": "application/json" } })).json();

  const { data: reglevelsData } = useQuery({
    queryKey: ["reglevels"],
    queryFn: () => fetchJSON("/api/reglevels"),
  });
  const { data: tagsData } = useQuery({
    queryKey: ["tags"],
    queryFn: () => fetchJSON("/api/tags"),
  });
  const { data: errorClassData } = useQuery({
    queryKey: ["errorClass"],
    queryFn: () => fetchJSON("/api/errorClass"),
  });
  const { data: extraKeysData } = useQuery({
    queryKey: ["extraKeys"],
    queryFn: () => fetchJSON("/api/extraKeys"),
  });

  // /api/vmTopo → 取三種枚舉
  const { data: vmTopoData } = useQuery({
    queryKey: ["vmTopo"],
    queryFn: () => fetchJSON("/api/vmTopo"),
  });

  const uniq = <T,>(arr: T[]) => Array.from(new Set(arr));

  const reglevelsOpts: Option[] = useMemo(() => {
    const names: string[] = uniq(
      (reglevelsData?.data ?? [])
        .map((row: any) => row?.regLevel?.name)
        .filter(Boolean)
    );
    return names.map((name) => ({ name, value: name }));
  }, [reglevelsData]);

  const tagOpts: Option[] = useMemo(() => {
    const names: string[] = uniq(
      (tagsData?.data ?? [])
        .map((row: any) => row?.tag?.name)
        .filter(Boolean)
    );
    return names.map((name) => ({ name, value: name }));
  }, [tagsData]);

  const errorClassOpts: Option[] = useMemo(() => {
    return (errorClassData?.data ?? []).map((row: any) => ({
      name: `${row?.severity} - ${row?.errorClass}`,
      value: String(row?.severity),
    }));
  }, [errorClassData]);

  const extraKeyOpts: Option[] = useMemo(() => {
    const names: string[] = uniq(
      (extraKeysData?.data ?? [])
        .map((row: any) => row?.extraKey?.name)
        .filter((n: any) => n !== undefined)
    );
    return names.map((name) => ({
      name: name === "" ? "(empty)" : name,
      value: name ?? "",
    }));
  }, [extraKeysData]);

  const vmPlatforms: Option[] = useMemo(() => {
    const names: string[] = uniq([
      "None",
      ...((vmTopoData?.data ?? [])
        .map((r: any) => r?.vmHostPlatform?.name)
        .filter(Boolean) as string[]),
    ]);
    return names.map((n) => ({ name: n, value: n }));
  }, [vmTopoData]);

  const vmHypervisors: Option[] = useMemo(() => {
    const names: string[] = uniq([
      "None",
      ...((vmTopoData?.data ?? [])
        .map((r: any) => r?.vmHostHypervisor?.name)
        .filter(Boolean) as string[]),
    ]);
    return names.map((n) => ({ name: n, value: n }));
  }, [vmTopoData]);

  const vmInterfaces: Option[] = useMemo(() => {
    const names: string[] = uniq([
      "None",
      ...((vmTopoData?.data ?? [])
        .map((r: any) => r?.vmHostInterface?.name)
        .filter(Boolean) as string[]),
    ]);
    return names.map((n) => ({ name: n, value: n }));
  }, [vmTopoData]);

  const clearState = () => {
    // basic
    setParentSuiteValue("");
    setOwnerValue("");
    setSuiteValue("");
    setBranch("");
    setStatus("");
    setReglevel("");
    setAction("");
    setTestCase("");

    // advanced left
    setOwnerAdv("");
    setReason("");
    setBuild("");
    setPhysiology("");
    setPlatform("");
    setVmPlatform("None");
    setSubSystem("");
    setDumpPerTopo(false);
    setCaseInsensitive(false);

    // advanced middle
    setErrorClassSeverity("");
    setTag("");
    setTeam("");
    setBeforeBuild("");
    setSubTopology("");
    setTopoStatus("");
    setVmHypervisor("None");
    setSubSystemBranch("");
    setSkipExpandAndGhost(true);
    setVmTopoAggr(false);

    // advanced right
    setInfoLevel("testOnly");
    setFramework("");
    setLastChangedSince("");
    setFid("");
    setRegressInclude("");
    setLocation("");
    setDts("");
    setAfterBuild("");
    setExtraKey("");
    setLastXDays("");
    setVmInterface("None");
    setIgnoreInvalidSubtopos(false);
  };

  return (
    <Card className="p-6 pb-24 relative grow">
      <Tabs defaultValue="basic" className="mb-6" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic" className="cursor-pointer">
            Basic Search
          </TabsTrigger>
          <TabsTrigger value="advanced" className="cursor-pointer">
            Advanced Search
          </TabsTrigger>
        </TabsList>

        {/* BASIC */}
        <TabsContent value="basic" className="mt-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="space-y-2">
              <label className="text-sm font-medium">Branch</label>
              <Select value={branch} onValueChange={setBranch}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {branches
                    .filter((b) => b.length > 0)
                    .map((b) => (
                      <SelectItem key={b} value={b}>
                        {b}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((s) => (
                    <SelectItem key={s.value} value={s.value}>
                      {s.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Reglevel</label>
              <Select value={reglevel} onValueChange={setReglevel}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {(reglevelsOpts.length ? reglevelsOpts : []).map((r) => (
                    <SelectItem key={r.value} value={r.value}>
                      {r.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Action</label>
              <Select value={action} onValueChange={setAction}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {actionOptions.map((a) => (
                    <SelectItem key={a.value} value={a.value}>
                      {a.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">TestCase</label>
              <Input
                placeholder="Search..."
                value={testcase}
                onChange={(e) => setTestCase(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Parent suite</label>
              <Combobox
                placeholder="Select..."
                value={parentSuiteValue}
                onChange={setParentSuiteValue}
                queryHook={useParentSuiteQuery}
                type="string"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Owner</label>
              <Combobox
                placeholder="Select..."
                value={ownerValue}
                onChange={setOwnerValue}
                queryHook={useOwnerQuery}
                type="user"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Suite</label>
              <Combobox
                placeholder="Select..."
                value={suiteValue}
                onChange={setSuiteValue}
                queryHook={useSuiteQuery}
                type="string"
              />
            </div>
          </div>
        </TabsContent>

        {/* ADVANCED */}
        <TabsContent value="advanced" className="mt-4">
          <div className="grid grid-cols-12 gap-5">
            {/* Row 1 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Owner</label>
              <Combobox
                placeholder="Select..."
                value={ownerAdv}
                onChange={setOwnerAdv}
                queryHook={useOwnerQuery}
                type="user"
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Error class severity</label>
              <Select
                value={errorClassSeverity}
                onValueChange={setErrorClassSeverity}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {errorClassOpts.map((o) => (
                    <SelectItem key={o.value} value={String(o.value)}>
                      {o.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">InfoLevel</label>
              <Select value={infoLevel} onValueChange={setInfoLevel}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {infoLevelOptions.map((o) => (
                    <SelectItem key={o.value} value={o.value}>
                      {o.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Row 2 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Reason</label>
              <Input
                placeholder="Select..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Tag</label>
              <Select value={tag} onValueChange={setTag}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {tagOpts.map((t) => (
                    <SelectItem key={t.value} value={t.value}>
                      {t.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Framework</label>
              <Input
                placeholder="Select..."
                value={framework}
                onChange={(e) => setFramework(e.target.value)}
              />
            </div>

            {/* Row 3 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Build</label>
              <Input
                placeholder="ex: BT-1688. Use [!build] for negative search"
                value={build}
                onChange={(e) => setBuild(e.target.value)}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Physiology</label>
              <Combobox
                placeholder="Select..."
                value={physiology}
                onChange={setPhysiology}
                queryHook={usePhysTopoQuery}
                type="string"
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Team</label>
              <Combobox
                placeholder="Select..."
                value={team}
                onChange={setTeam}
                queryHook={useTeamQuery}
                type="string"
              />
            </div>

            {/* Row 4 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Platform</label>
              <Input
                placeholder="Select..."
                value={platform}
                onChange={(e) => setPlatform(e.target.value)}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Before build</label>
              <Input
                placeholder="ex: BT-1688. Use [!build] for negative search"
                value={beforeBuild}
                onChange={(e) => setBeforeBuild(e.target.value)}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Last changed since</label>
              <Input
                type="date"
                value={lastChangedSince}
                onChange={(e) => setLastChangedSince(e.target.value)}
              />
            </div>

            {/* Row 5 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">VM platform</label>
              <Select value={vmPlatform} onValueChange={setVmPlatform}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {vmPlatforms.map((o) => (
                    <SelectItem key={o.value} value={o.value}>
                      {o.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Subtopology</label>
              <Combobox
                placeholder="Select..."
                value={subTopology}
                onChange={setSubTopology}
                queryHook={useSubTopoQuery}
                type="string"
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Regressinclude</label>
              <Select value={regressInclude} onValueChange={setRegressInclude}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {regressIncludeOptions.map((o) => (
                    <SelectItem key={o.value} value={o.value}>
                      {o.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Row 6 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Sub system</label>
              <Input
                placeholder="Select..."
                value={subSystem}
                onChange={(e) => setSubSystem(e.target.value)}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Topo status</label>
              <Select value={topoStatus} onValueChange={setTopoStatus}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((s) => (
                    <SelectItem key={s.value} value={s.value}>
                      {s.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Location</label>
              <Combobox
                placeholder="Search..."
                value={location}
                onChange={setLocation}
                queryHook={useLocationQuery}
                type="string"
              />
            </div>

            {/* Row 7 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">DTS</label>
              <Input
                placeholder="Select..."
                value={dts}
                onChange={(e) => setDts(e.target.value)}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">After build</label>
              <Input
                placeholder="ex: BT-1688. Use [!build] for negative search"
                value={afterBuild}
                onChange={(e) => setAfterBuild(e.target.value)}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">VM hypervisor</label>
              <Select value={vmHypervisor} onValueChange={setVmHypervisor}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {vmHypervisors.map((o) => (
                    <SelectItem key={o.value} value={o.value}>
                      {o.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Row 8 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Sub system branch</label>
              <Input
                placeholder="Select..."
                value={subSystemBranch}
                onChange={(e) => setSubSystemBranch(e.target.value)}
              />
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">ExtraKey</label>
              <Select value={extraKey} onValueChange={setExtraKey}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {extraKeyOpts.map((e) => (
                    <SelectItem key={String(e.value)} value={String(e.value)}>
                      {e.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">Last x days</label>
              <Input
                placeholder="e.g. 7"
                value={lastXDays}
                onChange={(e) => setLastXDays(e.target.value)}
              />
            </div>

            {/* Row 9 */}
            <div className="space-y-2 col-span-12 md:col-span-6 lg:col-span-4">
              <label className="text-sm font-medium">VM interface</label>
              <Select value={vmInterface} onValueChange={setVmInterface}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select..." />
                </SelectTrigger>
                <SelectContent>
                  {vmInterfaces.map((o) => (
                    <SelectItem key={o.value} value={o.value}>
                      {o.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Checkbox group at bottom */}
            <div className="col-span-12 pt-4 mt-2 border-t">
              <div className="grid grid-cols-12 gap-4">
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox
                    id="dump-per-topo"
                    checked={dumpPerTopo}
                    onCheckedChange={(v) => setDumpPerTopo(!!v)}
                  />
                  <label htmlFor="dump-per-topo" className="text-sm">
                    Dump per topo
                  </label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox
                    id="case-insensitive"
                    checked={caseInsensitive}
                    onCheckedChange={(v) => setCaseInsensitive(!!v)}
                  />
                  <label htmlFor="case-insensitive" className="text-sm">
                    Case insensitive search
                  </label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox
                    id="skip-expand-ghost"
                    checked={skipExpandAndGhost}
                    onCheckedChange={(v) => setSkipExpandAndGhost(!!v)}
                  />
                  <label htmlFor="skip-expand-ghost" className="text-sm">
                    Skip expand and ghost
                  </label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox
                    id="vm-topo-aggr"
                    checked={vmTopoAggr}
                    onCheckedChange={(v) => setVmTopoAggr(!!v)}
                  />
                  <label htmlFor="vm-topo-aggr" className="text-sm">
                    VmTopoAggr
                  </label>
                </div>
                <div className="flex items-center space-x-2 col-span-12 md:col-span-6 lg:col-span-4">
                  <Checkbox
                    id="ignore-invalid-subtopos"
                    checked={ignoreInvalidSubtopos}
                    onCheckedChange={(v) => setIgnoreInvalidSubtopos(!!v)}
                  />
                  <label htmlFor="ignore-invalid-subtopos" className="text-sm">
                    IgnoreInvalidSubtopos
                  </label>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <div className="absolute bottom-5 right-5 flex items-center justify-end space-x-2">
        <Button
          variant="outline"
          type="button"
          className="cursor-pointer"
          onClick={clearState}
        >
          <X className="mr-2 h-4 w-4" />
          Clear
        </Button>
        <Link
          to={"/results"}
          search={{
            // basic
            branch: branch,
            regLevel: reglevel,
            lastStatus: status,
            ownerTest: ownerValue,
            parentsuite: parentSuiteValue,
            suite: suiteValue,
            action: action,
            testCase: testcase,

            // advanced left
            owner: ownerAdv,
            reason,
            build,
            physiology,
            platform,
            vmPlatform,
            subSystem,
            dumpPerTopo: dumpPerTopo ? 1 : 0,
            caseInsensitive,

            // advanced middle
            errorClassSeverity,
            tag,
            team,
            beforeBuild,
            subTopology,
            topoStatus,
            vmHypervisor,
            subSystemBranch,
            skipExpandAndGhost,
            vmTopoAggr,

            // advanced right
            infoLevel,
            framework,
            lastChangedSince,
            fid,
            regressInclude,
            location,
            dts, // 後端在 index.ts 會轉成 DTS 參數
            afterBuild,
            extraKey,
            lastXDays,
            vmInterface,
            ignoreInvalidSubtopos,

            cmd: "query",
          }}
        >
          <Button type="submit" className="bg-primary cursor-pointer">
            <Search className="mr-2 h-4 w-4" />
            Search
          </Button>
        </Link>
      </div>
    </Card>
  );
}
